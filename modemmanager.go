package main

import (
	"fmt"
	"log"
	"os/exec"
	"strings"
	"time"
)

// resetModemManager resets the modem using mmcli commands
func getModemIP(job ProxyJob) (string, error) {
	if job.ModemPath == "" {
		return "", fmt.Errorf("modem path not configured")
	}

	cmd := exec.Command("sh", "-c", fmt.Sprintf("sms_tool -d %s at 'AT+CGPADDR' | awk -F'[,\"]' '/^\\+CGPADDR: 0,/ {print $3}'", job.ModemPath))
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get modem IP: %v", err)
	}
	return strings.TrimSpace(string(output)), nil
}

func resetModemManager(job ProxyJob) error {
	log.Printf("Checking current IP...")
	oldIP, err := getModemIP(job)
	if err != nil {
		log.Printf("Warning: Could not get initial IP: %v", err)
	} else {
		log.Printf("Current IP: %s", oldIP)
	}

	log.Printf("Refreshing modem connection...")

	// Run mmcli command to disable the modem
	cmd := exec.Command("mmcli", "-m", "any", "-d")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to disable modem: %v", err)
	}

	// Wait for a few seconds before re-enabling
	time.Sleep(5 * time.Second)

	// Run mmcli command to enable the modem
	cmd = exec.Command("mmcli", "-m", "any", "-e")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to enable modem: %v", err)
	}

	// Wait for modem to fully initialize
	time.Sleep(10 * time.Second)

	// Check new IP
	newIP, err := getModemIP(job)
	if err != nil {
		log.Printf("Warning: Could not get new IP: %v", err)
	} else {
		log.Printf("New IP: %s", newIP)

		// If IP didn't change and we had both IPs
		if oldIP != "" && newIP != "" && oldIP == newIP {
			log.Printf("IP didn't change, trying full modem reset...")

			// Try full modem reset
			cmd = exec.Command("mmcli", "-m", "any", "-r")
			if err := cmd.Run(); err != nil {
				return fmt.Errorf("failed to reset modem: %v", err)
			}

			// Wait for modem to restart
			time.Sleep(20 * time.Second)

			// Check IP again
			finalIP, err := getModemIP(job)
			if err != nil {
				log.Printf("Warning: Could not get final IP: %v", err)
			} else {
				log.Printf("Final IP after reset: %s", finalIP)
			}
		}
	}

	log.Printf("Successfully refreshed modem connection")
	return nil
}
