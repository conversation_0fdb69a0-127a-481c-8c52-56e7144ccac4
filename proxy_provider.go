package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Struct untuk parsing JSON response
type ProxyResponse struct {
	Providers map[string]Provider `json:"providers"`
}

type Provider struct {
	Name        string  `json:"name"`
	Type        string  `json:"type"`
	VehicleType string  `json:"vehicleType"`
	Proxies     []Proxy `json:"proxies"`
}

type Proxy struct {
	Name    string    `json:"name"`
	Type    string    `json:"type"`
	Alive   bool      `json:"alive"`
	History []History `json:"history"`
}

type History struct {
	Time  string `json:"time"`
	Delay int    `json:"delay"`
}

// Fungsi untuk mendapatkan data proxy dari API
func getProxyData() (*ProxyResponse, error) {
	config, err := GetDefaultConfig()
	if err != nil {
		return nil, fmt.Errorf("error getting default config: %v", err)
	}

	// Create client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Create request using configuration
	url := fmt.Sprintf("http://%s:%s/providers/proxies/", config.APISettings.LocalIP, config.APISettings.APIPort)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// Add authorization header using secret
	req.Header.Add("Authorization", "Bearer "+config.APISettings.Secret)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	// Check status code
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("error: Status Code %d, Response: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var proxyResponse ProxyResponse
	err = json.Unmarshal(body, &proxyResponse)
	if err != nil {
		return nil, fmt.Errorf("error parsing JSON: %v", err)
	}

	return &proxyResponse, nil
}

// DisplayProxies prints available proxies in a formatted way
func DisplayProxies(response *ProxyResponse) {
	fmt.Println("Available Proxies:")
	for name, provider := range response.Providers {
		// Only show HTTP or File vehicle types
		if provider.VehicleType == "HTTP" || provider.VehicleType == "File" {
			fmt.Printf("Provider: %s\n", name)
			for _, proxy := range provider.Proxies {
				fmt.Printf("  - %s\n", proxy.Name)
			}
		}
	}
}
