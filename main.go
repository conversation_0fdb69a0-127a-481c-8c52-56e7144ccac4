package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

const Version = "1.0.0"

// refreshByModemType refreshes all modems of specified type
func refreshByModemType(modemType string) error {
	config, err := GetDefaultConfig()
	if err != nil {
		return fmt.Errorf("error reading config: %v", err)
	}

	for _, job := range config.ProxyJobs {
		if job.ModemType == modemType {
			if err := refreshIP(job); err != nil {
				log.Printf("Error refreshing %s: %v", job.Name, err)
			}
		}
	}
	return nil
}

func main() {
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "help":
			showHelp()
			return
		case "proxies":
			if err := showProxies(); err != nil {
				log.Fatalf("Error showing proxies: %v", err)
			}
			return
		case "ping":
			if len(os.Args) < 3 {
				log.Fatal("Usage: model-tools ping <proxy-name>")
			}
			proxyName := os.Args[2]
			startSinglePing(proxyName)
			return
		case "iprefresh":
			if len(os.Args) < 3 {
				log.Fatal("Usage: model-tools iprefresh <android|modemmanager>")
			}
			modemType := os.Args[2]
			if err := refreshByModemType(modemType); err != nil {
				log.Fatalf("Error refreshing %s modems: %v", modemType, err)
			}
			log.Printf("Successfully refreshed all %s modems", modemType)
			return
		case "start-all":
			startMonitoring(DefaultConfigPath)
			return
		default:
			showHelp()
			return
		}
	}

	// Jika tidak ada command, tampilkan help
	showHelp()
}

func startSinglePing(proxyName string) {
	log.Printf("Modem Tools v%s - Single Proxy Monitor", Version)

	config, err := GetDefaultConfig()
	if err != nil {
		log.Fatalf("Error loading config: %v", err)
	}

	// Find the specified proxy
	var targetJob ProxyJob
	found := false
	for _, job := range config.ProxyJobs {
		if job.Name == proxyName {
			targetJob = job
			found = true
			break
		}
	}

	if !found {
		log.Fatalf("Proxy '%s' not found in configuration", proxyName)
	}

	// Set up proxy address
	targetJob.ProxyAddr = config.APISettings.LocalIP + ":" + config.ProxiesSettings.Proxies

	// Setup signal handling
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Start ping loop in goroutine
	stopChan := make(chan struct{})
	go pingLoop(targetJob, stopChan)
	log.Printf("Started monitoring proxy %s. Press Ctrl+C to stop.", targetJob.Name)

	// Wait for interrupt signal
	<-sigCh
	log.Println("Shutting down...")
	close(stopChan)

	// Give some time for goroutines to clean up
	time.Sleep(time.Second)
}

func startMonitoring(configPath string) {
	log.Printf("Modem Tools v%s - Multi Proxy Monitor", Version)

	// Load config
	config, err := getConfigFromJSON(configPath)
	if err != nil {
		log.Fatalf("Error loading config: %v", err)
	}

	// Setup signal handling
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Start monitoring each proxy in a goroutine
	stopChan := make(chan struct{})
	var jobs []ProxyJob

	for _, job := range config.ProxyJobs {
		job.ProxyAddr = config.APISettings.LocalIP + ":" + config.ProxiesSettings.Proxies
		jobs = append(jobs, job)
		log.Printf("Starting monitoring proxy %s", job.Name)
		go pingLoop(job, stopChan)
	}

	log.Printf("Started monitoring %d proxy(s)", len(jobs))

	// Wait for interrupt signal
	<-sigCh
	log.Println("Shutting down...")
	close(stopChan)

	// Give some time for goroutines to clean up
	time.Sleep(time.Second)
}

func showHelp() {
	fmt.Println("Usage: modem-tools <command>")
	fmt.Println("\nAvailable commands:")
	fmt.Println("  start-all                       Start monitoring all configured proxies")
	fmt.Println("  proxies                         List available proxy names and their status")
	fmt.Println("  iprefresh <android|modemmanager> Refresh IP for all modems of specified type")
	fmt.Println("  help                            Show this help message")
}

func showProxies() error {
	// Get proxy data from API
	proxyResponse, err := getProxyData()
	if err != nil {
		return fmt.Errorf("failed to get proxy data: %v", err)
	}

	// Display proxy information
	DisplayProxies(proxyResponse)
	return nil
}
