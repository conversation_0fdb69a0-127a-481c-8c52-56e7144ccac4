package main

import (
	"fmt"
	"sync"
)

// JobManager handles all proxy monitoring tasks
// Using a map to store jobs by proxy name
// and a mutex to secure concurrent access
type JobManager struct {
	jobs map[string]*Job // Store all jobs by proxy name
	mu   sync.RWMutex    // Mutex for thread-safe access
	wg   sync.WaitGroup  // WaitGroup to wait for all goroutines
}

// Job represents a running proxy monitoring task
type Job struct {
	ProxyJob  ProxyJob      // Proxy information to monitor
	StopChan  chan struct{} // Channel to stop monitoring
	IsRunning bool          // Current job status
}

// NewJobManager creates a new job manager instance
func NewJobManager() *JobManager {
	return &JobManager{
		jobs: make(map[string]*Job),
	}
}

// LoadJobs loads proxy list from config file
// and prepares a job for each proxy
func (jm *JobManager) LoadJobs() error {
	config, err := GetDefaultConfig()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %v", err)
	}

	jm.mu.Lock()
	defer jm.mu.Unlock()

	// Setup proxy address for each job
	for _, proxyJob := range config.ProxyJobs {
		proxyJob.ProxyAddr = config.APISettings.LocalIP + ":" + config.ProxiesSettings.Proxies
		jm.jobs[proxyJob.Name] = &Job{
			ProxyJob:  proxyJob,
			StopChan:  make(chan struct{}),
			IsRunning: false,
		}
	}
	return nil
}

// StartAllJobs starts monitoring for all proxies in parallel
// that are not already running
func (jm *JobManager) StartAllJobs() {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	for _, job := range jm.jobs {
		if !job.IsRunning {
			jm.wg.Add(1)
			job.IsRunning = true
			go func(j *Job) {
				defer jm.wg.Done()
				defer func() {
					jm.mu.Lock()
					j.IsRunning = false
					jm.mu.Unlock()
				}()
				pingLoop(j.ProxyJob, j.StopChan)
			}(job)
		}
	}
}

// StopAllJobs stops all running jobs
// and waits for them to finish
func (jm *JobManager) StopAllJobs() {
	jm.mu.Lock()
	for _, job := range jm.jobs {
		if job.IsRunning {
			close(job.StopChan)
			job.StopChan = make(chan struct{}) // Create new channel for future use
			job.IsRunning = false
		}
	}
	jm.mu.Unlock()
	jm.wg.Wait() // Wait for all goroutines to finish
}

// StartJob starts monitoring for a specific proxy
// by its proxy name
func (jm *JobManager) StartJob(proxyName string) error {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	job, exists := jm.jobs[proxyName]
	if !exists {
		return fmt.Errorf("proxy %s not found in configuration", proxyName)
	}

	if job.IsRunning {
		return fmt.Errorf("proxy %s is already running", proxyName)
	}

	jm.wg.Add(1)
	job.IsRunning = true
	go func() {
		defer jm.wg.Done()
		defer func() {
			jm.mu.Lock()
			job.IsRunning = false
			jm.mu.Unlock()
		}()
		pingLoop(job.ProxyJob, job.StopChan)
	}()

	return nil
}

// StopJob stops monitoring for a specific proxy
func (jm *JobManager) StopJob(proxyName string) error {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	job, exists := jm.jobs[proxyName]
	if !exists {
		return fmt.Errorf("proxy %s not found", proxyName)
	}

	if !job.IsRunning {
		return fmt.Errorf("proxy %s is not running", proxyName)
	}

	close(job.StopChan)
	job.StopChan = make(chan struct{}) // Create new channel for future use
	job.IsRunning = false
	return nil
}
