package main

import (
	"fmt"
	"log"
)

// refreshIP performs IP refresh based on modem type
// This is called automatically when a proxy reaches timeout threshold
func refreshIP(job ProxyJob) error {
	switch job.ModemType {
	case "android":
		if job.DeviceID == "" {
			return fmt.Errorf("device ID is required for android modem %s", job.Name)
		}
		if err := toggleAirplaneMode(job.DeviceID); err != nil {
			return fmt.Errorf("failed to refresh android modem %s: %v", job.Name, err)
		}
		log.Printf("Successfully refreshed IP for android modem %s", job.Name)

	case "modemmanager":
		if err := resetModemManager(job); err != nil {
			return fmt.Errorf("failed to refresh modemmanager %s: %v", job.Name, err)
		}
		log.Printf("Successfully refreshed IP for modemmanager %s", job.Name)

	default:
		return fmt.Errorf("unknown modem type %s for %s", job.ModemType, job.Name)
	}

	return nil
}
