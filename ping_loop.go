package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"time"
)

type PingResponse struct {
	Delay   int    `json:"delay,omitempty"`
	Message string `json:"message,omitempty"`
}

func pingLoop(job ProxyJob, stopCh chan struct{}) {
	config, err := GetDefaultConfig()
	if err != nil {
		log.Printf("[%s] Error loading config: %v", job.Name, err)
		return
	}

	// Parse ping interval from config
	pingInterval, err := strconv.Atoi(config.PingSetting.PingInterval)
	if err != nil {
		log.Printf("[%s] Invalid ping interval in config: %v, using default 2s", job.Name, err)
		pingInterval = 2 // default 2 seconds
	}

	// Parse timeout threshold for IP refresh from config
	timeoutThreshold, err := strconv.Atoi(config.PingSetting.TimeoutIPRefresh)
	if err != nil {
		log.Printf("[%s] Invalid timeout threshold in config: %v, using default 8s", job.Name, err)
		timeoutThreshold = 8 // default 8 seconds
	}

	// Setup client
	client := &http.Client{
		Timeout: time.Duration(timeoutThreshold) * time.Second,
	}

	// Create ping URL with timeout from config
	testURL := fmt.Sprintf("http://%s:%s/proxies/%s/delay?timeout=%d&url=%s",
		config.APISettings.LocalIP,
		config.APISettings.APIPort,
		job.Name,
		timeoutThreshold*1000, // convert to milliseconds
		config.ProxiesSettings.URLTest)

	log.Printf("[%s] Starting ping loop with interval: %ds, timeout: %ds",
		job.Name, pingInterval, timeoutThreshold)

	ticker := time.NewTicker(time.Duration(pingInterval) * time.Second)
	defer ticker.Stop()

	failCount := 0
	for {
		select {
		case <-stopCh:
			log.Printf("[%s] Stopping ping loop", job.Name)
			return

		case <-ticker.C:
			// Create request
			req, err := http.NewRequest("GET", testURL, nil)
			if err != nil {
				log.Printf("[%s] Error creating request: %v", job.Name, err)
				continue
			}
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", config.APISettings.Secret))

			// Do request
			resp, err := client.Do(req)
			if err != nil {
				errMsg := "Connection timeout"
				if err != context.DeadlineExceeded {
					errMsg = "Connection failed"
				}
				log.Printf("[%s] %s", job.Name, errMsg)
				failCount++
				if failCount >= timeoutThreshold {
					refreshIP(job)
					failCount = 0
				}
				continue
			}

			// Read response
			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err != nil {
				log.Printf("[%s] Error reading response: %v", job.Name, err)
				failCount++
				if failCount >= timeoutThreshold {
					refreshIP(job)
					failCount = 0
				}
				continue
			}

			// Parse response
			var pingResp PingResponse
			if err := json.Unmarshal(body, &pingResp); err != nil {
				log.Printf("[%s] Error parsing response: %v", job.Name, err)
				failCount++
				if failCount >= timeoutThreshold {
					refreshIP(job)
					failCount = 0
				}
				continue
			}

			// Handle error messages
			if pingResp.Message != "" {
				log.Printf("[%s] Ping failed: %s", job.Name, pingResp.Message)
				failCount++
				if failCount >= timeoutThreshold {
					refreshIP(job)
					failCount = 0
				}
				continue
			}

			// Success case
			if pingResp.Delay > 0 {
				log.Printf("[%s] Ping successful: %dms", job.Name, pingResp.Delay)
				failCount = 0 // Reset counter only on successful ping
			} else {
				log.Printf("[%s] Invalid delay: %d", job.Name, pingResp.Delay)
				failCount++
				if failCount >= timeoutThreshold {
					refreshIP(job)
					failCount = 0
				}
			}
		}
	}
}
