package main

import (
	"encoding/json"
	"fmt"
	"os"
)

const DefaultConfigPath = "/etc/modem_tools/config.json"

type Config struct {
	APISettings     APISettings     `json:"api_settings"`
	ProxiesSettings ProxiesSettings `json:"proxies_settings"`
	PingSetting     PingSetting     `json:"ping_setting"`
	ProxyJobs       []ProxyJob      `json:"proxy_jobs"`
}

type ADBDevices struct {
	ID string `json:"id"`
}

type APISettings struct {
	Secret  string `json:"secret"`
	LocalIP string `json:"local_ip"`
	APIPort string `json:"api_port"`
}

type ProxiesSettings struct {
	ServerIP    string `json:"server_ip"`
	Proxies     string `json:"proxies"`
	URLTest     string `json:"url_test"`
	Delay       string `json:"delay"`
	ModemDevice string `json:"modem_device"`
}

type PingSetting struct {
	PingInterval     string `json:"ping_interval"`
	TimeoutIPRefresh string `json:"timeout_iprefresh"`
}

type ProxyJob struct {
	Name      string `json:"name"`
	ModemType string `json:"modem_type"`
	DeviceID  string `json:"device_id"`
	ModemPath string `json:"modem_path"`
	ProxyAddr string `json:"proxy_addr"`
}

// Fungsi untuk membaca konfigurasi dari file JSON
func getConfigFromJSON(filePath string) (*Config, error) {
	// Baca isi file JSON
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %v", filePath, err)
	}

	var config Config
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config data from %s: %v", filePath, err)
	}

	// Lakukan validasi jika diperlukan
	if config.APISettings.Secret == "" {
		return nil, fmt.Errorf("secret is empty in config file %s", filePath)
	}
	if config.APISettings.LocalIP == "" {
		return nil, fmt.Errorf("local_ip is empty in config file %s", filePath)
	}
	if config.APISettings.APIPort == "" {
		return nil, fmt.Errorf("api_port is empty in config file %s", filePath)
	}
	if config.PingSetting.PingInterval == "" {
		return nil, fmt.Errorf("ping_interval is empty in config file %s", filePath)
	}
	if config.PingSetting.TimeoutIPRefresh == "" {
		return nil, fmt.Errorf("timeout_iprefresh is empty in config file %s", filePath)
	}

	return &config, nil
}

func GetDefaultConfig() (*Config, error) {
	return getConfigFromJSON(DefaultConfigPath)
}
