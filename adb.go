package main

import (
	"fmt"
	"log"
	"os/exec"
	"time"
)

func getAndroidIP(deviceID string) (string, error) {
	cmd := exec.Command("sh", "-c", fmt.Sprintf("adb -s %s shell ip -f inet addr show | awk '/inet.*rmnet/ {print $2}' | cut -d'/' -f1 | head -n 1", deviceID))
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get android IP: %v", err)
	}
	return string(output), nil
}

func toggleAirplaneMode(deviceID string) error {
	if deviceID == "" {
		return fmt.Errorf("device ID is required")
	}

	// Get initial IP
	oldIP, err := getAndroidIP(deviceID)
	if err != nil {
		log.Printf("Warning: Could not get initial IP: %v", err)
	} else {
		log.Printf("Current IP: %s", oldIP)
	}

	log.Printf("Refreshing IP for device: %s...", deviceID)

	commands := []string{
		// Turn airplane mode ON
		fmt.Sprintf("adb -s %s shell settings put global airplane_mode_on 1", deviceID),
		fmt.Sprintf("adb -s %s shell am broadcast -a android.intent.action.AIRPLANE_MODE --ez state true", deviceID),
		"sleep 3",
		// Turn airplane mode OFF
		fmt.Sprintf("adb -s %s shell settings put global airplane_mode_on 0", deviceID),
		fmt.Sprintf("adb -s %s shell am broadcast -a android.intent.action.AIRPLANE_MODE --ez state false", deviceID),
		"sleep 10",
	}

	// Execute each command quietly
	for _, cmd := range commands {
		command := exec.Command("sh", "-c", cmd)
		if err := command.Run(); err != nil {
			return fmt.Errorf("failed to refresh IP: %v", err)
		}
	}

	// Wait a bit for IP to stabilize
	time.Sleep(5 * time.Second)

	// Check new IP
	newIP, err := getAndroidIP(deviceID)
	if err != nil {
		log.Printf("Warning: Could not get new IP: %v", err)
	} else {
		log.Printf("New IP: %s", newIP)

		// If IP didn't change and we had both IPs
		if oldIP != "" && newIP != "" && oldIP == newIP {
			log.Printf("Warning: IP did not change after refresh")
		}
	}

	log.Printf("Successfully refreshed IP for device: %s", deviceID)
	return nil
}
